<template>
  <div class="nav-bar">
    <div class="left">
      <el-icon class="icon" @click="onClickLeft">
        <ArrowLeft />
      </el-icon>
    </div>
    <div v-if="!showBackOnly" class="center">
      <div class="title">{{ title }}</div>
    </div>
    <div v-if="!showBackOnly" class="right">
      <el-dropdown
        v-if="isPictureFile"
        trigger="click"
        @visible-change="onDropdownVisibleChange"
      >
        <el-icon class="icon icon-plus">
          <More />
        </el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <el-button link @click="addEmptyPage">纸张</el-button>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button link @click="checkCameraPermission">拍照</el-button>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button link @click="addNewPicture">图库</el-button>
            </el-dropdown-item>
            <el-dropdown-item divided>
              <el-button link type="danger" @click="deleteCurrentPage">
                删除当前页
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
          <el-dropdown-item divided @click.stop>
            <TopNavBarConfig />
          </el-dropdown-item>
          <el-dropdown-item divided>
            <el-button link @click="resetTour">操作说明</el-button>
          </el-dropdown-item>
        </template>
      </el-dropdown>
    </div>
  </div>
  <el-dialog
    v-model="imagePreviewVisible"
    :show-close="false"
    :destroy-on-close="true"
    :align-center="true"
    :fullscreen="true"
    :z-index="3000"
    :append-to-body="true"
  >
    <template #header> </template>
    <div style="margin: -26px -16px -16px -16px">
      <ImagePreview
        v-if="newImageFiles && newImageFiles.length > 0"
        :image-files="newImageFiles"
        @confirm="onAddConfirm"
        @cancel="onAddCancel"
      />
    </div>
  </el-dialog>
  <input
    ref="cameraInputRef"
    type="file"
    accept="image/*"
    capture="environment"
    @change="takeNewPhotoChange"
    style="display: none"
  />
  <input
    ref="photoInputRef"
    type="file"
    accept="image/*"
    multiple
    @change="addNewPictureChange"
    style="display: none"
  />
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import { ArrowLeft, More } from "@element-plus/icons-vue";
import { useEventListener } from "vue-hooks-plus";
import dayjs from "dayjs";
import { useAppStore } from "@/stores/app.js";
import { getFileMeta, putFileMeta } from "@/lib/FileList.js";
import { asyncReadAsDataURL, updateFileCoverData } from "@/lib/FileCover.js";
import {
  deleteNoteByFileIdIndex,
  deletePenLinesByFileIdIndex,
  deleteRectByFileIdIndex,
} from "@/lib/RectDatabase.js";
import TopNavBarConfig from "@/components/NavBar/TopNavBarConfig.vue";
import ImagePreview from "@/components/ImagePreview/ImagePreview.vue";
import { APP_ACTION_TYPE, LayerType } from "@/constant.js";

defineProps({
  showBackOnly: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["dropdown-visible-change", "reset-tour"]);

const router = useRouter();
const appStore = useAppStore();
const cameraInputRef = ref(null);
const photoInputRef = ref(null);
const lastAction = ref("");
const confirmAddNewImageFiles = ref([]);
const isInsertFileDataToStorage = computed(
  () => appStore.insertFileDataToStorage
);

const title = ref("");
watch(
  () => appStore.currentFileName,
  () => (title.value = trunkString(appStore.currentFileName)),
  {
    immediate: true,
  }
);

function trunkString(string) {
  // string max length = 24
  return string.length > 24 ? string.substring(0, 24) + "..." : string;
}

const onClickLeft = async () => {
  if (appStore.lastVisitedFolderId) {
    const folder = await getFileMeta(appStore.lastVisitedFolderId);
    if (!folder) {
      await router.replace("/");
    } else {
      await router.replace({
        path: "/file",
        query: {
          folder: folder.id,
          parent: folder.parentId,
        },
      });
    }
  } else {
    await router.replace("/");
  }
};

function onDropdownVisibleChange(visible) {
  emits("dropdown-visible-change", visible);
}

const imagePreviewVisible = ref(false);
const newImageFiles = ref([]);

const isPictureFile = computed(
  () => appStore.currentFileMeta?.extension !== "pdf"
);

function resetTour() {
  emits("reset-tour");
}

function addEmptyPage() {
  // todo: add empty page
}

function checkCameraPermission() {
  if (typeof ReactNativeWebView !== "undefined") {
    const data = {
      action: "check-camera-permission",
    };
    // eslint-disable-next-line no-undef
    ReactNativeWebView.postMessage(JSON.stringify(data));
  } else {
    ElMessage.error("无法获取拍照权限");
  }
}

function takeNewPhoto() {
  if (cameraInputRef.value) {
    cameraInputRef.value.value = "";
    cameraInputRef.value.click();
  }
}

function takeNewPhotoChange(event) {
  if (event.target.files.length > 0) {
    const files = [];
    for (let i = 0; i < event.target.files.length; i++) {
      files.push(event.target.files[i]);
    }
    newImageFiles.value = files;
    imagePreviewVisible.value = true;
  }
}

function addNewPicture() {
  if (photoInputRef.value) {
    photoInputRef.value.value = "";
    photoInputRef.value.click();
  }
}

function addNewPictureChange(event) {
  if (event.target.files.length > 0) {
    const files = [];
    for (let i = 0; i < event.target.files.length; i++) {
      files.push(event.target.files[i]);
    }
    newImageFiles.value = files;
    imagePreviewVisible.value = true;
  }
}

async function onAddConfirm(newFiles) {
  newImageFiles.value = [];
  imagePreviewVisible.value = false;
  ElLoading.service({ lock: true });

  const iframe = document.getElementById("mobsf4");
  if (iframe) {
    const data = {
      layerType: LayerType.APP,
      type: APP_ACTION_TYPE.DEL_PAGE,
    };
    iframe.contentWindow.postMessage(data, "*");
  }
  lastAction.value = "addNewImage";
  confirmAddNewImageFiles.value = newFiles;
}

async function saveFileDataToStorage(pageNumber) {
  const timestamp = appStore.currentFileId;
  const newFiles = confirmAddNewImageFiles.value;
  for (let i = 0; i < newFiles.length; i++) {
    const file = newFiles[i];
    if (!file.uid) file.uid = dayjs().unix() + i;
    const fileData = await asyncReadAsDataURL(file);
    if (fileData?.data) {
      if (typeof ReactNativeWebView !== "undefined") {
        const data = {
          action: "save-file-data-to-storage",
          timestamp: timestamp.toString(),
          data: fileData.data,
          index: file.uid,
        };
        // eslint-disable-next-line no-undef
        ReactNativeWebView.postMessage(JSON.stringify(data));
      }
    }
  }

  if (isInsertFileDataToStorage.value) {
    appStore.currentFile = [
      ...appStore.currentFile.slice(0, pageNumber + 1),
      ...newFiles,
      ...appStore.currentFile.slice(pageNumber + 1),
    ];
  } else {
    appStore.currentFile = [...appStore.currentFile, ...newFiles];
  }

  const fileUidList = appStore.currentFile.map((file) => Number(file.uid));
  const fileMeta = await getFileMeta(appStore.currentFileId);
  fileMeta.fileUidList = fileUidList;
  await putFileMeta(fileMeta);

  // todo: post message by store action
  const iframe = document.getElementById("mobsf4");
  if (iframe) {
    const data = {
      layerType: LayerType.APP,
      type: APP_ACTION_TYPE.ADD_IMG,
      file: [...appStore.currentFile],
      fileUid: fileUidList,
      fileId: appStore.currentFileId,
    };
    iframe.contentWindow.postMessage(data, "*");
  }
}

function onAddCancel() {
  newImageFiles.value = [];
  imagePreviewVisible.value = false;
}

function deleteCurrentPage() {
  if (
    Array.isArray(appStore.currentFile) &&
    appStore.currentFile.length === 1
  ) {
    ElMessageBox.alert("当前页面是最后一张，不能删除", "警告");
    return;
  }

  // todo: post message by store action
  const iframe = document.getElementById("mobsf4");
  if (iframe) {
    const data = {
      layerType: LayerType.APP,
      type: APP_ACTION_TYPE.DEL_PAGE,
    };
    iframe.contentWindow.postMessage(data, "*");
  }
  lastAction.value = "deleteCurrentPage";
}

useEventListener("message", (event) => {
  const { layerType, type } = event.data;
  if (layerType === LayerType.APP && type === APP_ACTION_TYPE.DEL_PAGE) {
    const { page: pageNumber } = event.data;
    if (
      Number.isInteger(pageNumber) &&
      lastAction.value === "deleteCurrentPage"
    ) {
      ElMessageBox.confirm(
        `确认删除当前活动页面${pageNumber + 1}？`,
        "Warning",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          confirmDelCurrentPage(pageNumber);
        })
        .catch(() => {});
    } else if (
      Number.isInteger(pageNumber) &&
      lastAction.value === "addNewImage"
    ) {
      saveFileDataToStorage(pageNumber);
    }
  } else if (event?.data?.action === "expo-take-photo") {
    takeNewPhoto();
  }
});

async function confirmDelCurrentPage(pageNumber) {
  ElLoading.service({ lock: true });

  const timestamp = appStore.currentFileId;
  if (typeof ReactNativeWebView !== "undefined") {
    const data = {
      action: "delete-single-file",
      timestamp: timestamp.toString(),
      index: pageNumber,
    };
    // eslint-disable-next-line no-undef
    ReactNativeWebView.postMessage(JSON.stringify(data));
  }

  await deleteRectByFileIdIndex(appStore.currentFileId, pageNumber);
  await deletePenLinesByFileIdIndex(appStore.currentFileId, pageNumber);
  await deleteNoteByFileIdIndex(appStore.currentFileId, pageNumber);

  appStore.currentFile = appStore.currentFile.filter(
    (_, index) => index !== pageNumber
  );
  if (pageNumber === 0 && appStore.currentFile.length > 0) {
    await updateFileCoverData(appStore.currentFileId, appStore.currentFile[0]);
  }
  // todo: post message by store action
  const iframe = document.getElementById("mobsf4");
  if (iframe) {
    const data = {
      layerType: LayerType.APP,
      type: APP_ACTION_TYPE.ADD_IMG,
      file: [...appStore.currentFile],
      fileId: appStore.currentFileId,
    };
    iframe.contentWindow.postMessage(data, "*");
  }

  ElMessage.success("删除成功");
}
</script>

<style scoped>
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 25px;
}

.left,
.right {
  display: flex;
  align-items: center;
  flex: 1;
}

.left {
  justify-content: flex-start;
}

.right {
  justify-content: flex-end;
}

.center {
  display: flex;
  align-items: center;
  flex: 2;
  justify-content: center;
}

.icon {
  margin-right: 10px;
  font-size: 24px;
}

.icon-plus {
  margin-left: 20px;
}
</style>
