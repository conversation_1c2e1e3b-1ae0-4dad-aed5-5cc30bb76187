import Dexie from "dexie";

const globalForIndexedDb = window;

export const createFileListDbInstance = () => {
  // 检查是否已存在实例且实例可用
  if (globalForIndexedDb.fileListDb) {
    try {
      // 检查实例是否仍然有效
      if (
        globalForIndexedDb.fileListDb.isOpen() &&
        globalForIndexedDb.fileListDb.name === "fileList"
      ) {
        return globalForIndexedDb.fileListDb;
      }
    } catch (error) {
      // 如果检查过程中出现错误，说明实例不可用，需要重新创建
      console.warn("Existing fileListDb instance is not usable:", error);
    }
  }

  // 创建新实例
  const fileListDbInstance = new Dexie("fileList");
  fileListDbInstance.version(1).stores({
    files: "&id, parentId, name, type",
    covers: "&id",
  });

  globalForIndexedDb.fileListDb = fileListDbInstance;
  return fileListDbInstance;
};

export const createAppDbInstance = () => {
  // 检查是否已存在实例且实例可用
  if (globalForIndexedDb.appDb) {
    try {
      // 检查实例是否仍然有效
      if (
        globalForIndexedDb.appDb.isOpen() &&
        globalForIndexedDb.appDb.name === "AppDatabase"
      ) {
        return globalForIndexedDb.appDb;
      }
    } catch (error) {
      // 如果检查过程中出现错误，说明实例不可用，需要重新创建
      console.warn("Existing appDb instance is not usable:", error);
    }
  }

  // 创建新实例
  const appDbInstance = new Dexie("AppDatabase");
  appDbInstance.version(2).stores({
    colorTagData: "&id",
    penData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    rectData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    noteData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    ocrData: "&uid",
    configData: "&key",
  });

  globalForIndexedDb.appDb = appDbInstance;
  return appDbInstance;
};
