import Dexie from "dexie";

const globalForIndexedDb = window;

export const createFileListDbInstance = () => {
  const fileListDbInstance = new Dexie("fileList");
  fileListDbInstance.version(1).stores({
    files: "&id, parentId, name, type",
    covers: "&id",
  });
  if (!globalForIndexedDb.fileListDb) {
    globalForIndexedDb.fileListDb = fileListDbInstance;
  }
  return fileListDbInstance;
};

export const createAppDbInstance = () => {
  const appDbInstance = new Dexie("AppDatabase");
  appDbInstance.version(2).stores({
    colorTagData: "&id",
    penData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    rectData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    noteData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    ocrData: "&uid",
    configData: "&key",
  });

  if (!globalForIndexedDb.appDb) {
    globalForIndexedDb.appDb = appDbInstance;
  }
  return appDbInstance;
};
