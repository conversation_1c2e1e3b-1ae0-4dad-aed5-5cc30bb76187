// @ts-expect-error no declaration file for AppDatabase
import { createAppDbInstance } from "@/lib/AppDatabase";

const db = createAppDbInstance();

// Config: isEditorFirstLaunch (boolean), default: true
export async function getIsEditorFirstLaunch(): Promise<boolean> {
  const config = await db.configData.get("isEditorFirstLaunch");
  return config === undefined ? true : config.value;
}

export async function setIsEditorFirstLaunch(value: boolean) {
  if (typeof value !== "boolean") {
    return;
  }
  return db.configData.put({ key: "isEditorFirstLaunch", value });
}

// Config: showResultRect (boolean), default: false
export async function getShowResultRect(): Promise<boolean> {
  const config = await db.configData.get("showResultRect");
  return config === undefined ? false : config.value;
}

export async function setShowResultRect(value: boolean) {
  if (typeof value !== "boolean") {
    return;
  }
  return db.configData.put({ key: "showResultRect", value });
}

// Config: ocrEngineType ('JS' | 'WASM'), default: 'JS'
export async function getOcrEngineType(): Promise<"JS" | "WASM"> {
  const config = await db.configData.get("ocrEngineType");
  return config === undefined ? "JS" : config.value;
}

export async function setOcrEngineType(value: "JS" | "WASM") {
  if (value !== "JS" && value !== "WASM") {
    return;
  }
  return db.configData.put({ key: "ocrEngineType", value });
}
