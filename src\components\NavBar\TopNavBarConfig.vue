<template>
  <el-checkbox v-model="showResultRect" @change="handleChange">
    显示识别框
  </el-checkbox>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElCheckbox } from "element-plus";
import { getShowResultRect, setShowResultRect } from "@/lib/ConfigDatabase.ts";

const showResultRect = ref(true);

onMounted(() => {
  readShowResultRect();
});

async function readShowResultRect() {
  showResultRect.value = await getShowResultRect();
}

const handleChange = (value: boolean | string | number) => {
  if (typeof value === "boolean") {
    setShowResultRect(value).then(() => {
      readShowResultRect();
    });
  }
};
</script>
